"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { 
  Shield, 
  Smartphone, 
  Monitor, 
  AlertTriangle, 
  Clock, 
  MapPin, 
  Loader2,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Key,
  Bell
} from "lucide-react"

interface SecuritySettings {
  two_factor_enabled: boolean
  two_factor_method: 'sms' | 'email' | 'authenticator' | null
  login_notifications: boolean
  suspicious_activity_alerts: boolean
  device_management_enabled: boolean
  session_timeout: number
  password_last_changed: string
  trusted_devices: string[]
  blocked_ips: string[]
}

interface LoginSession {
  id: string
  session_id: string
  ip_address: string
  user_agent: string
  device_info: {
    browser: string
    os: string
    device_type: 'desktop' | 'mobile' | 'tablet'
    is_trusted: boolean
  }
  location?: {
    country: string
    city: string
    region: string
  }
  login_time: string
  last_activity: string
  is_active: boolean
  logout_time?: string
}

interface SecurityEvent {
  id: string
  event_type: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  ip_address: string
  timestamp: string
  resolved: boolean
}

export function SecurityDashboard() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [settings, setSettings] = useState<SecuritySettings | null>(null)
  const [sessions, setSessions] = useState<LoginSession[]>([])
  const [events, setEvents] = useState<SecurityEvent[]>([])
  const [activeSessionsCount, setActiveSessionsCount] = useState(0)

  useEffect(() => {
    if (user) {
      fetchSecurityData()
    }
  }, [user])

  const fetchSecurityData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/users/${user!.id}/security?include=settings,sessions,events,active_sessions`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch security data')
      }

      const data = await response.json()
      setSettings(data.settings)
      setSessions(data.sessions || [])
      setEvents(data.events || [])
      setActiveSessionsCount(data.active_sessions_count || 0)
    } catch (error) {
      console.error('Error fetching security data:', error)
      toast({
        title: "Error",
        description: "Failed to load security information",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateSecuritySetting = async (key: string, value: any) => {
    try {
      const response = await fetch(`/api/users/${user!.id}/security`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ [key]: value })
      })

      if (!response.ok) {
        throw new Error('Failed to update security setting')
      }

      const result = await response.json()
      setSettings(result.settings)

      toast({
        title: "Security setting updated",
        description: "Your security preferences have been saved.",
      })
    } catch (error) {
      console.error('Error updating security setting:', error)
      toast({
        title: "Error",
        description: "Failed to update security setting",
        variant: "destructive",
      })
    }
  }

  const endSession = async (sessionId: string) => {
    try {
      const response = await fetch(`/api/users/${user!.id}/security`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: 'end_session', 
          data: { session_id: sessionId, reason: 'manual' }
        })
      })

      if (!response.ok) {
        throw new Error('Failed to end session')
      }

      // Refresh sessions
      await fetchSecurityData()

      toast({
        title: "Session ended",
        description: "The session has been terminated successfully.",
      })
    } catch (error) {
      console.error('Error ending session:', error)
      toast({
        title: "Error",
        description: "Failed to end session",
        variant: "destructive",
      })
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'tablet':
        return <Smartphone className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'destructive'
      case 'high':
        return 'destructive'
      case 'medium':
        return 'default'
      case 'low':
        return 'secondary'
      default:
        return 'secondary'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!settings) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>Failed to load security settings</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Shield className="h-5 w-5" />
        <h3 className="text-lg font-semibold">Security & Privacy</h3>
      </div>

      <Tabs defaultValue="settings" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Authentication & Access
              </CardTitle>
              <CardDescription>
                Manage your login security and authentication methods
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Two-Factor Authentication</Label>
                  <p className="text-sm text-muted-foreground">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Switch
                  checked={settings.two_factor_enabled}
                  onCheckedChange={(checked) => updateSecuritySetting('two_factor_enabled', checked)}
                />
              </div>

              {settings.two_factor_enabled && (
                <div className="space-y-2">
                  <Label>Two-Factor Method</Label>
                  <Select 
                    value={settings.two_factor_method || 'email'} 
                    onValueChange={(value) => updateSecuritySetting('two_factor_method', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                      <SelectItem value="authenticator">Authenticator App</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Session Management</h4>
                <div className="space-y-2">
                  <Label>Session Timeout (minutes)</Label>
                  <Select 
                    value={settings.session_timeout.toString()} 
                    onValueChange={(value) => updateSecuritySetting('session_timeout', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                      <SelectItem value="480">8 hours</SelectItem>
                      <SelectItem value="1440">24 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Security Notifications
              </CardTitle>
              <CardDescription>
                Configure when you want to be notified about security events
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Login Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when someone logs into your account
                  </p>
                </div>
                <Switch
                  checked={settings.login_notifications}
                  onCheckedChange={(checked) => updateSecuritySetting('login_notifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Suspicious Activity Alerts</Label>
                  <p className="text-sm text-muted-foreground">
                    Get alerted about unusual account activity
                  </p>
                </div>
                <Switch
                  checked={settings.suspicious_activity_alerts}
                  onCheckedChange={(checked) => updateSecuritySetting('suspicious_activity_alerts', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Device Management</Label>
                  <p className="text-sm text-muted-foreground">
                    Track and manage devices that access your account
                  </p>
                </div>
                <Switch
                  checked={settings.device_management_enabled}
                  onCheckedChange={(checked) => updateSecuritySetting('device_management_enabled', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Sessions ({activeSessionsCount})</CardTitle>
              <CardDescription>
                Manage your active login sessions across different devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {sessions.length > 0 ? (
                  sessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-start space-x-3">
                        {getDeviceIcon(session.device_info.device_type)}
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <p className="font-medium">
                              {session.device_info.browser} on {session.device_info.os}
                            </p>
                            {session.is_active ? (
                              <Badge variant="default">Active</Badge>
                            ) : (
                              <Badge variant="secondary">Inactive</Badge>
                            )}
                            {session.device_info.is_trusted && (
                              <Badge variant="outline">Trusted</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {session.ip_address}
                            {session.location && ` • ${session.location.city}, ${session.location.country}`}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Last active: {new Date(session.last_activity).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      {session.is_active && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => endSession(session.id)}
                        >
                          End Session
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    No active sessions found
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Security Events</CardTitle>
              <CardDescription>
                Monitor security-related activities on your account
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {events.length > 0 ? (
                  events.map((event) => (
                    <div key={event.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <div className="flex-shrink-0 mt-1">
                        {event.resolved ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="text-sm font-medium">{event.description}</p>
                          <Badge variant={getSeverityColor(event.severity) as any}>
                            {event.severity}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {new Date(event.timestamp).toLocaleString()} • {event.ip_address}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground text-center py-8">
                    No security events found
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Trusted Devices</CardTitle>
              <CardDescription>
                Manage devices that you trust for quick access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Monitor className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">Device management coming soon</p>
                <p className="text-sm text-muted-foreground mt-2">
                  You'll be able to manage trusted devices and review device access history
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
