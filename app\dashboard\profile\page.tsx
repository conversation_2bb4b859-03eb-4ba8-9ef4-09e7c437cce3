"use client"

import { useState, useEffect, useRef } from "react"
import { useAuth } from "@/components/auth-provider"
import { useToast } from "@/hooks/use-toast"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Camera, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Calendar, Shield, Bell, Eye, Activity, Settings, Download, History } from "lucide-react"
import { listUserAudio, getUserWithSubcollections, checkUsernameAvailability, type HierarchicalAudioData, type HierarchicalUserData } from "@/lib/firebase-service"
import { SecurityDashboard } from "@/components/security/security-dashboard"

interface UploadStats {
  total: number;
  approved: number;
  pending: number;
  rejected: number;
  minutes: number;
}

interface Stats {
  totalContributions: number;
  approvedContributions: number;
  pendingContributions: number;
  rejectedContributions: number;
  totalMinutes: number;
  uploadStats: UploadStats;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: string
  notifications: {
    email: boolean
    push: boolean
    training_updates: boolean
    review_notifications: boolean
    system_announcements: boolean
    weekly_summary: boolean
  }
  privacy: {
    profile_public: boolean
    stats_public: boolean
    allow_contact: boolean
    show_activity: boolean
  }
  accessibility: {
    high_contrast: boolean
    large_text: boolean
    reduced_motion: boolean
    screen_reader: boolean
  }
  audio: {
    auto_play: boolean
    default_volume: number
    quality_preference: 'low' | 'medium' | 'high'
    download_format: 'mp3' | 'wav' | 'original'
  }
  interface: {
    compact_mode: boolean
    show_tooltips: boolean
    default_page_size: number
    auto_save: boolean
  }
}

interface ActivityEvent {
  id: string
  type: string
  description: string
  timestamp: string
  metadata?: any
}

export default function ProfilePage() {
  const { user, updateProfile, sendVerificationEmail } = useAuth()
  const { toast } = useToast()
  const [isEditing, setIsEditing] = useState(false)
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isLoadingStats, setIsLoadingStats] = useState(true)
  const [isSendingVerification, setIsSendingVerification] = useState(false)
  const [stats, setStats] = useState<Stats>({
    totalContributions: 0,
    approvedContributions: 0,
    pendingContributions: 0,
    rejectedContributions: 0,
    totalMinutes: 0,
    uploadStats: {
      total: 0,
      approved: 0,
      pending: 0,
      rejected: 0,
      minutes: 0
    }
  })
  const [userProfile, setUserProfile] = useState<HierarchicalUserData | null>(null)
  const [isUploadingPicture, setIsUploadingPicture] = useState(false)
  const [preferences, setPreferences] = useState<UserPreferences | null>(null)
  const [activityHistory, setActivityHistory] = useState<ActivityEvent[]>([])
  const [isLoadingPreferences, setIsLoadingPreferences] = useState(false)
  const [isLoadingActivity, setIsLoadingActivity] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Form states
  const [name, setName] = useState(user?.name || "")
  const [username, setUsername] = useState(user?.username || "")
  const [email, setEmail] = useState(user?.email || "")
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoadingStats(true);

        // Fetch user profile with statistics
        const userProfileData = await getUserWithSubcollections(user!.id, ['profile', 'statistics', 'preferences']);
        setUserProfile(userProfileData);

        // Fetch audio recordings using new API
        const audioRecordings = await listUserAudio(user!.id, { limit: 1000 });

        // Calculate upload stats from hierarchical data
        const uploadStats = {
          total: audioRecordings.length,
          approved: audioRecordings.filter(r => r.review?.status?.action === 'approved').length,
          pending: audioRecordings.filter(r => (r.review?.status?.action || 'pending') === 'pending').length,
          rejected: audioRecordings.filter(r => r.review?.status?.action === 'rejected').length,
          minutes: Math.round(audioRecordings.reduce((acc, r) => acc + (r.duration || 0), 0) / 60)
        };

        setStats({
          totalContributions: uploadStats.total,
          approvedContributions: uploadStats.approved,
          pendingContributions: uploadStats.pending,
          rejectedContributions: uploadStats.rejected,
          totalMinutes: uploadStats.minutes,
          uploadStats
        });

        // Fetch user preferences
        await fetchUserPreferences();

        // Fetch activity history
        await fetchActivityHistory();
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to fetch user data",
          variant: "destructive",
        });
      } finally {
        setIsLoadingStats(false);
      }
    };

    if (user) {
      fetchData();
    }
  }, [user]);

  const fetchUserPreferences = async () => {
    try {
      setIsLoadingPreferences(true);
      const response = await fetch(`/api/users/${user!.id}/preferences`);
      if (response.ok) {
        const preferencesData = await response.json();
        setPreferences(preferencesData);
      }
    } catch (error) {
      console.error("Error fetching preferences:", error);
    } finally {
      setIsLoadingPreferences(false);
    }
  };

  const fetchActivityHistory = async () => {
    try {
      setIsLoadingActivity(true);
      const response = await fetch(`/api/users/${user!.id}/activity?limit=20`);
      if (response.ok) {
        const activityData = await response.json();
        setActivityHistory(activityData.activities || []);
      }
    } catch (error) {
      console.error("Error fetching activity:", error);
    } finally {
      setIsLoadingActivity(false);
    }
  };

  const checkUsernameAvailabilityLocal = async (username: string) => {
    if (username === user?.username) return true

    // Use direct Firebase service to check username availability
    return await checkUsernameAvailability(username)
  }

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    if (!username.match(/^[a-zA-Z0-9_]+$/)) {
      setError("Username can only contain letters, numbers, and underscores")
      setIsLoading(false)
      return
    }

    try {
      const isUsernameAvailable = await checkUsernameAvailabilityLocal(username)
      if (!isUsernameAvailable) {
        setError("Username is already taken")
        setIsLoading(false)
        return
      }

      await updateProfile({ name, username })
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      })
      setIsEditing(false)
    } catch (err: any) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendVerificationEmail = async () => {
    setIsSendingVerification(true)
    try {
      await sendVerificationEmail()
      toast({
        title: "Verification email sent",
        description: "Please check your inbox and follow the instructions to verify your email.",
      })
    } catch (err: any) {
      toast({
        title: "Error",
        description: err.message || "Failed to send verification email",
        variant: "destructive",
      })
    } finally {
      setIsSendingVerification(false)
    }
  }

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault()
    setError("")
    setIsLoading(true)

    if (newPassword !== confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation password must match.",
        variant: "destructive",
      })
      setIsLoading(false)
      return
    }

    try {
      await updateProfile({
        email: email !== user?.email ? email : undefined,
        currentPassword: currentPassword || undefined,
        newPassword: newPassword || undefined
      })
      toast({
        title: "Password updated",
        description: "Your password has been changed successfully.",
      })
      setCurrentPassword("")
      setNewPassword("")
      setConfirmPassword("")
    } catch (err: any) {
      if (err.message.includes("verify your current email")) {
        toast({
          title: "Email verification required",
          description: err.message,
          action: (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSendVerificationEmail}
              disabled={isSendingVerification}
            >
              {isSendingVerification ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                "Send Verification Email"
              )}
            </Button>
          ),
        })
      } else {
        setError(err.message)
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleProfilePictureChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file",
        variant: "destructive",
      })
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB",
        variant: "destructive",
      })
      return
    }

    setIsUploadingPicture(true)
    try {
      await updateProfile({ profilePicture: file })
      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been updated successfully.",
      })
    } catch (err: any) {
      toast({
        title: "Error",
        description: err.message || "Failed to update profile picture",
        variant: "destructive",
      })
    } finally {
      setIsUploadingPicture(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const handlePreferenceUpdate = async (section: string, updates: any) => {
    try {
      const response = await fetch(`/api/users/${user!.id}/preferences`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ [section]: updates })
      });

      if (!response.ok) {
        throw new Error('Failed to update preferences');
      }

      const updatedPreferences = await response.json();
      setPreferences(updatedPreferences.preferences);

      toast({
        title: "Preferences updated",
        description: "Your preferences have been saved successfully.",
      });
    } catch (error) {
      console.error("Error updating preferences:", error);
      toast({
        title: "Error",
        description: "Failed to update preferences",
        variant: "destructive",
      });
    }
  };

  const handleNotificationToggle = (key: string, value: boolean) => {
    if (!preferences) return;

    const updatedNotifications = {
      ...preferences.notifications,
      [key]: value
    };

    handlePreferenceUpdate('notifications', updatedNotifications);
  };

  const handlePrivacyToggle = (key: string, value: boolean) => {
    if (!preferences) return;

    const updatedPrivacy = {
      ...preferences.privacy,
      [key]: value
    };

    handlePreferenceUpdate('privacy', updatedPrivacy);
  };

  const handleAccessibilityToggle = (key: string, value: boolean) => {
    if (!preferences) return;

    const updatedAccessibility = {
      ...preferences.accessibility,
      [key]: value
    };

    handlePreferenceUpdate('accessibility', updatedAccessibility);
  };

  if (!user) {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>Please log in to view your profile</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
        <p className="text-muted-foreground mt-2">View and update your account information</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={user?.profilePicture || `https://avatar.vercel.sh/${user?.username}`} />
                  <AvatarFallback>{user?.username?.slice(0, 2).toUpperCase()}</AvatarFallback>
                </Avatar>
                <Button
                  variant="outline"
                  size="icon"
                  className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploadingPicture}
                >
                  {isUploadingPicture ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Camera className="h-4 w-4" />
                  )}
                </Button>
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  onChange={handleProfilePictureChange}
                />
              </div>
              <div>
                <CardTitle>{user?.username}</CardTitle>
                <CardDescription>{user?.email}</CardDescription>
                <div className="flex gap-2 mt-2">
                  <Badge>{user?.role === "admin" ? "Administrator" : "Contributor"}</Badge>
                  {!user?.emailVerified && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      Unverified Email
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="profile">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="profile">Profile</TabsTrigger>
                <TabsTrigger value="password">Password</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
                <TabsTrigger value="preferences">Preferences</TabsTrigger>
                <TabsTrigger value="privacy">Privacy</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>
              <TabsContent value="profile" className="space-y-4 pt-4">
                <form onSubmit={handleProfileUpdate}>
                  <div className="space-y-4">
                    {error && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                      </Alert>
                    )}
                    
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        disabled={!isEditing}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="username">Username</Label>
                      <Input
                        id="username"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        disabled={!isEditing}
                        required
                      />
                    </div>
                    
                    {isEditing ? (
                      <div className="flex justify-end gap-2">
                        <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
                          Cancel
                        </Button>
                        <Button type="submit" disabled={isLoading}>
                          {isLoading ? "Saving..." : "Save Changes"}
                        </Button>
                      </div>
                    ) : (
                      <Button type="button" onClick={() => setIsEditing(true)} className="w-full">
                        Edit Profile
                      </Button>
                    )}
                  </div>
                </form>
              </TabsContent>
              <TabsContent value="password" className="space-y-4 pt-4">
                <div className="flex items-center gap-2 mb-4">
                  <Shield className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Password & Email</h3>
                </div>
                <form onSubmit={handlePasswordChange}>
                  <div className="space-y-4">
                    {error && (
                      <Alert variant="destructive">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                      </Alert>
                    )}
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input
                        id="current-password"
                        type="password"
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input
                        id="new-password"
                        type="password"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input
                        id="confirm-password"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                      />
                    </div>
                    
                    <Button type="submit" className="w-full" disabled={isLoading}>
                      {isLoading ? "Updating..." : "Change Password"}
                    </Button>
                  </div>
                </form>
              </TabsContent>

              <TabsContent value="security" className="space-y-4 pt-4">
                <SecurityDashboard />
              </TabsContent>

              <TabsContent value="preferences" className="space-y-4 pt-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Preferences</h3>
                </div>

                {isLoadingPreferences ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : preferences ? (
                  <div className="space-y-6">
                    {/* Theme Settings */}
                    <div className="space-y-3">
                      <h4 className="font-medium">Appearance</h4>
                      <div className="space-y-2">
                        <Label htmlFor="theme">Theme</Label>
                        <Select
                          value={preferences.theme}
                          onValueChange={(value) => handlePreferenceUpdate('theme', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="light">Light</SelectItem>
                            <SelectItem value="dark">Dark</SelectItem>
                            <SelectItem value="system">System</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="language">Language</Label>
                        <Select
                          value={preferences.language}
                          onValueChange={(value) => handlePreferenceUpdate('language', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="ar">العربية</SelectItem>
                            <SelectItem value="masalit">Masalit</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <Separator />

                    {/* Notification Settings */}
                    <div className="space-y-3">
                      <h4 className="font-medium">Notifications</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Email Notifications</Label>
                            <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                          </div>
                          <Switch
                            checked={preferences.notifications.email}
                            onCheckedChange={(checked) => handleNotificationToggle('email', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Training Updates</Label>
                            <p className="text-sm text-muted-foreground">Get notified about training progress</p>
                          </div>
                          <Switch
                            checked={preferences.notifications.training_updates}
                            onCheckedChange={(checked) => handleNotificationToggle('training_updates', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Review Notifications</Label>
                            <p className="text-sm text-muted-foreground">Get notified when your uploads are reviewed</p>
                          </div>
                          <Switch
                            checked={preferences.notifications.review_notifications}
                            onCheckedChange={(checked) => handleNotificationToggle('review_notifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Weekly Summary</Label>
                            <p className="text-sm text-muted-foreground">Receive weekly contribution summaries</p>
                          </div>
                          <Switch
                            checked={preferences.notifications.weekly_summary}
                            onCheckedChange={(checked) => handleNotificationToggle('weekly_summary', checked)}
                          />
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Accessibility Settings */}
                    <div className="space-y-3">
                      <h4 className="font-medium">Accessibility</h4>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>High Contrast</Label>
                            <p className="text-sm text-muted-foreground">Increase contrast for better visibility</p>
                          </div>
                          <Switch
                            checked={preferences.accessibility.high_contrast}
                            onCheckedChange={(checked) => handleAccessibilityToggle('high_contrast', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Large Text</Label>
                            <p className="text-sm text-muted-foreground">Use larger text sizes</p>
                          </div>
                          <Switch
                            checked={preferences.accessibility.large_text}
                            onCheckedChange={(checked) => handleAccessibilityToggle('large_text', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Reduced Motion</Label>
                            <p className="text-sm text-muted-foreground">Minimize animations and transitions</p>
                          </div>
                          <Switch
                            checked={preferences.accessibility.reduced_motion}
                            onCheckedChange={(checked) => handleAccessibilityToggle('reduced_motion', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Failed to load preferences</p>
                )}
              </TabsContent>

              <TabsContent value="privacy" className="space-y-4 pt-4">
                <div className="flex items-center gap-2 mb-4">
                  <Eye className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Privacy Settings</h3>
                </div>

                {isLoadingPreferences ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : preferences ? (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Public Profile</Label>
                          <p className="text-sm text-muted-foreground">Allow others to view your profile information</p>
                        </div>
                        <Switch
                          checked={preferences.privacy.profile_public}
                          onCheckedChange={(checked) => handlePrivacyToggle('profile_public', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Public Statistics</Label>
                          <p className="text-sm text-muted-foreground">Show your contribution statistics publicly</p>
                        </div>
                        <Switch
                          checked={preferences.privacy.stats_public}
                          onCheckedChange={(checked) => handlePrivacyToggle('stats_public', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Allow Contact</Label>
                          <p className="text-sm text-muted-foreground">Allow other users to contact you</p>
                        </div>
                        <Switch
                          checked={preferences.privacy.allow_contact}
                          onCheckedChange={(checked) => handlePrivacyToggle('allow_contact', checked)}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                          <Label>Show Activity</Label>
                          <p className="text-sm text-muted-foreground">Display your recent activity to others</p>
                        </div>
                        <Switch
                          checked={preferences.privacy.show_activity}
                          onCheckedChange={(checked) => handlePrivacyToggle('show_activity', checked)}
                        />
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-3">
                      <h4 className="font-medium">Data Management</h4>
                      <div className="space-y-2">
                        <Button variant="outline" className="w-full justify-start">
                          <Download className="h-4 w-4 mr-2" />
                          Download My Data
                        </Button>
                        <p className="text-sm text-muted-foreground">
                          Download a copy of all your data including uploads, transcriptions, and activity history.
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="text-muted-foreground">Failed to load privacy settings</p>
                )}
              </TabsContent>

              <TabsContent value="activity" className="space-y-4 pt-4">
                <div className="flex items-center gap-2 mb-4">
                  <Activity className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Activity History</h3>
                </div>

                {isLoadingActivity ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {activityHistory.length > 0 ? (
                      activityHistory.map((activity, index) => (
                        <div key={activity.id || index} className="flex items-start space-x-3 p-3 border rounded-lg">
                          <div className="flex-shrink-0 mt-1">
                            {activity.type === 'audio_upload' && <Calendar className="h-4 w-4 text-blue-500" />}
                            {activity.type === 'audio_approved' && <Badge className="h-4 w-4 text-green-500" />}
                            {activity.type === 'audio_rejected' && <AlertCircle className="h-4 w-4 text-red-500" />}
                            {!['audio_upload', 'audio_approved', 'audio_rejected'].includes(activity.type) &&
                              <Activity className="h-4 w-4 text-gray-500" />}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium">{activity.description}</p>
                            <p className="text-xs text-muted-foreground">
                              {new Date(activity.timestamp).toLocaleString()}
                            </p>
                            {activity.metadata && (
                              <div className="mt-1">
                                {activity.metadata.duration && (
                                  <span className="text-xs text-muted-foreground">
                                    Duration: {Math.round(activity.metadata.duration)}s
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                          {activity.metadata?.status && (
                            <Badge variant={
                              activity.metadata.status === 'approved' ? 'default' :
                              activity.metadata.status === 'rejected' ? 'destructive' : 'secondary'
                            }>
                              {activity.metadata.status}
                            </Badge>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">No activity history available</p>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contribution Statistics</CardTitle>
            <CardDescription>Your contribution history and statistics</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingStats ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin" />
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Total Contributions</p>
                    <p className="text-2xl font-bold">{stats.totalContributions}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Total Minutes</p>
                    <p className="text-2xl font-bold">{stats.totalMinutes}</p>
                  </div>
                </div>

                {/* Upload Statistics */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Upload Statistics</h3>
                  <div className="mt-4 space-y-4">
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Approved</span>
                        <span>{stats.uploadStats.approved}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${stats.uploadStats.total > 0 ? (stats.uploadStats.approved / stats.uploadStats.total) * 100 : 0}%` }}
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Pending</span>
                        <span>{stats.uploadStats.pending}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-yellow-600 h-2 rounded-full"
                          style={{ width: `${stats.uploadStats.total > 0 ? (stats.uploadStats.pending / stats.uploadStats.total) * 100 : 0}%` }}
                        />
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Rejected</span>
                        <span>{stats.uploadStats.rejected}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-red-600 h-2 rounded-full"
                          style={{ width: `${stats.uploadStats.total > 0 ? (stats.uploadStats.rejected / stats.uploadStats.total) * 100 : 0}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
          <CardFooter>
            <p className="text-sm text-muted-foreground">
              Thank you for your contributions to the Masalit language preservation project!
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}
