rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Audio files - users can upload their own, admins can access all
    match /audio/{userId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null &&
        exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Models - admin only
    match /models/{allPaths=**} {
      allow read, write: if request.auth != null &&
        exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
        get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Profile pictures - users can upload their own
    match /profile-pictures/{userId}/{fileName} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Public files - read only for authenticated users
    match /public/{allPaths=**} {
      allow read: if request.auth != null;
    }
  }
}
