"use client"

import { useEffect, useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { useFocusedLanguage } from "@/components/focused-language-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import { Users, FileAudio, Brain, Shield, Activity, TrendingUp, Clock, CheckCircle, XCircle, AlertTriangle, Database, Server, Zap, Upload, UserPlus } from "lucide-react"
import { collection, getDocs, query, where, orderBy, limit, getDoc, doc } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { Loader2 } from "lucide-react"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Bar, Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
)

// Safe system health calculation with error suppression
async function getSystemHealthSafely(): Promise<number | string> {
  try {
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';

    // Add timeout to prevent hanging
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3 second timeout

    const response = await fetch(`${backendUrl}/api/health/detailed`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();

      if (data.system?.cpu_usage && data.system?.memory?.percent) {
        const cpuUsage = parseFloat(data.system.cpu_usage.replace('%', ''));
        const memoryUsage = parseFloat(data.system.memory.percent.replace('%', ''));

        // Calculate health (inverted - low usage = high health)
        const cpuHealth = Math.max(0, 100 - cpuUsage);
        const memoryHealth = Math.max(0, 100 - memoryUsage);
        const gpuHealth = 100; // No GPU data, assume healthy

        // Weighted average: CPU (40%), Memory (40%), GPU (20%)
        const healthScore = Math.round(
          (cpuHealth * 0.4) + (memoryHealth * 0.4) + (gpuHealth * 0.2)
        );

        console.log(`✅ System Health: ${healthScore}% (CPU: ${cpuUsage}%, Memory: ${memoryUsage}%)`);
        return healthScore;
      }
    }

    // Fallback when API doesn't return expected data
    console.log("⚠️ Backend responded but no valid system data found");
    return "Unavailable";

  } catch (error) {
    // Suppress all errors and return unavailable
    console.log("🔄 Backend not accessible, showing unavailable");
    return "Unavailable";
  }
}

interface SystemStats {
  totalUsers: number
  totalAudio: number
  totalMinutes: number
  approvedAudio: number
  pendingAudio: number
  rejectedAudio: number
  activeUsers: number
  verifiedUsers: number
  adminUsers: number
  disabledUsers: number
  newUsersThisMonth: number
  systemHealth: number | string
}

interface RecentActivity {
  id: string
  type: 'audio_upload' | 'user_registration' | 'upload' | 'approval' | 'rejection'
  description: string
  timestamp: any
  user: string
  metadata?: any
  title?: string
  username?: string
  status?: string
}

export function AdminDashboard() {
  const { user } = useAuth()
  const { t, isRTL } = useFocusedLanguage()
  const [loading, setLoading] = useState(true)
  const [systemStats, setSystemStats] = useState<SystemStats>({
    totalUsers: 0,
    totalAudio: 0,
    totalMinutes: 0,
    approvedAudio: 0,
    pendingAudio: 0,
    rejectedAudio: 0,
    activeUsers: 0,
    verifiedUsers: 0,
    adminUsers: 0,
    disabledUsers: 0,
    newUsersThisMonth: 0,
    systemHealth: "Loading..."
  })
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [chartData, setChartData] = useState({
    userGrowth: {
      labels: [],
      datasets: [{
        label: t('totalUsers'),
        data: [],
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
      }]
    },
    audioStats: {
      labels: [t('approved'), t('pending'), t('rejected')],
      datasets: [{
        label: t('totalAudio'),
        data: [0, 0, 0],
        backgroundColor: ['#22c55e', '#eab308', '#ef4444'],
      }]
    }
  })

  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        // Fetch all users with enhanced data
        const usersSnapshot = await getDocs(collection(db, "users"))
        const allUsers = usersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))
        const totalUsers = allUsers.length

        // Calculate user statistics
        const verifiedUsers = allUsers.filter(user => user.email_verified === true).length
        const adminUsers = allUsers.filter(user => user.role === 'admin').length
        const disabledUsers = allUsers.filter(user => user.isDisabled === true).length

        // Calculate new users this month
        const now = new Date()
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        const newUsersThisMonth = allUsers.filter(user => {
          try {
            let createdAt: Date
            if (user.created_at?.toDate && typeof user.created_at.toDate === 'function') {
              createdAt = user.created_at.toDate()
            } else if (user.created_at) {
              createdAt = new Date(user.created_at)
            } else {
              return false
            }
            return createdAt >= thisMonth
          } catch (error) {
            return false
          }
        }).length

        // Fetch all audio with enhanced data including review status
        const audioSnapshot = await getDocs(collection(db, "audio"))
        const allAudio = []

        for (const audioDoc of audioSnapshot.docs) {
          const audioData = {
            id: audioDoc.id,
            ...audioDoc.data()
          }

          // Fetch review status from subcollection
          try {
            const reviewStatusDoc = await getDoc(doc(db, 'audio', audioDoc.id, 'review', 'status'))
            if (reviewStatusDoc.exists()) {
              audioData.review = {
                status: reviewStatusDoc.data()
              }
            }
          } catch (error) {
            console.warn(`Could not fetch review status for audio ${audioDoc.id}:`, error)
          }

          allAudio.push(audioData)
        }

        const totalAudio = allAudio.length
        const totalMinutes = Math.round(allAudio.reduce((acc, audio) => acc + (audio.duration || 0), 0) / 60)

        // Enhanced audio status tracking
        const approvedAudio = allAudio.filter(audio => audio.review?.status?.action === 'approved' || audio.action === 'approved').length
        const pendingAudio = allAudio.filter(audio => {
          const action = audio.review?.status?.action || audio.action
          return !action || action === 'pending'
        }).length
        const rejectedAudio = allAudio.filter(audio => audio.review?.status?.action === 'rejected' || audio.action === 'rejected').length

        // Calculate active users (users who uploaded in last 30 days)
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
        const activeUsers = new Set(
          allAudio
            .filter(audio => {
              try {
                let createdAt: Date
                if (audio.created_at?.toDate && typeof audio.created_at.toDate === 'function') {
                  createdAt = audio.created_at.toDate()
                } else if (audio.created_at) {
                  createdAt = new Date(audio.created_at)
                } else {
                  return false
                }
                return createdAt > thirtyDaysAgo
              } catch (error) {
                return false
              }
            })
            .map(audio => audio.user_id)
        ).size

        // Fetch recent activity
        const recentActivity = []

        // Get recent audio uploads (last 10)
        const recentAudio = allAudio
          .sort((a, b) => {
            try {
              const timeA = a.created_at?.toDate ? a.created_at.toDate() : new Date(a.created_at || 0)
              const timeB = b.created_at?.toDate ? b.created_at.toDate() : new Date(b.created_at || 0)
              return timeB.getTime() - timeA.getTime()
            } catch {
              return 0
            }
          })
          .slice(0, 5)

        for (const audio of recentAudio) {
          const user = allUsers.find(u => u.id === audio.user_id)

          // Get the best available title/filename with enhanced fallbacks
          let audioTitle = audio.title || audio.filename || audio.name

          // If still no title, try to get transcription content for a preview
          if (!audioTitle || audioTitle === 'Untitled') {
            try {
              const transcriptionDoc = await getDoc(doc(db, 'audio', audio.id, 'transcriptions', 'primary'))
              if (transcriptionDoc.exists()) {
                const transcriptionData = transcriptionDoc.data()
                if (transcriptionData.content) {
                  // Use first 30 characters of transcription as title
                  const preview = transcriptionData.content.substring(0, 30).trim()
                  if (preview) {
                    const ellipsis = transcriptionData.content.length > 30 ? '...' : ''
                    audioTitle = `"${preview}${ellipsis}"`
                  }
                }
              }
            } catch (error) {
              console.warn(`Could not fetch transcription for audio ${audio.id}:`, error)
            }
          }

          // Final fallback
          if (!audioTitle || audioTitle === 'Untitled') {
            const timestamp = audio.created_at?.toDate ? audio.created_at.toDate() : new Date(audio.created_at || 0)
            audioTitle = `Recording ${timestamp.toLocaleDateString()}`
          }

          recentActivity.push({
            id: `audio-${audio.id}`,
            type: 'audio_upload',
            description: `${user?.username || 'Unknown user'} uploaded: ${audioTitle}`,
            timestamp: audio.created_at,
            user: user?.username || 'Unknown',
            metadata: {
              audioId: audio.id,
              title: audioTitle,
              filename: audio.filename,
              duration: audio.duration,
              status: audio.review?.status?.action || audio.action || 'pending'
            }
          })
        }

        // Get recent user registrations (last 5)
        const recentUsers = allUsers
          .sort((a, b) => {
            try {
              const timeA = a.created_at?.toDate ? a.created_at.toDate() : new Date(a.created_at || 0)
              const timeB = b.created_at?.toDate ? b.created_at.toDate() : new Date(b.created_at || 0)
              return timeB.getTime() - timeA.getTime()
            } catch {
              return 0
            }
          })
          .slice(0, 3)

        for (const user of recentUsers) {
          recentActivity.push({
            id: `user-${user.id}`,
            type: 'user_registration',
            description: `New user registered: ${user.username}`,
            timestamp: user.created_at,
            user: user.username,
            metadata: {
              userId: user.id,
              email: user.email,
              role: user.role,
              verified: user.email_verified
            }
          })
        }

        // Sort all activity by timestamp
        recentActivity.sort((a, b) => {
          try {
            const timeA = a.timestamp?.toDate ? a.timestamp.toDate() : new Date(a.timestamp || 0)
            const timeB = b.timestamp?.toDate ? b.timestamp.toDate() : new Date(b.timestamp || 0)
            return timeB.getTime() - timeA.getTime()
          } catch {
            return 0
          }
        })

        setRecentActivity(recentActivity.slice(0, 10))

        setSystemStats({
          totalUsers,
          totalAudio,
          totalMinutes,
          approvedAudio,
          pendingAudio,
          rejectedAudio,
          activeUsers,
          verifiedUsers,
          adminUsers,
          disabledUsers,
          newUsersThisMonth,
          systemHealth: await getSystemHealthSafely() // Safe system health with fallback
        })



        // Update chart data
        setChartData(prev => ({
          ...prev,
          audioStats: {
            labels: [t('approved'), t('pending'), t('rejected')],
            datasets: [{
              label: t('totalAudio'),
              data: [approvedAudio, pendingAudio, rejectedAudio],
              backgroundColor: ['#22c55e', '#eab308', '#ef4444'],
            }]
          }
        }))

        // Generate user growth data (mock data for last 6 months)
        const monthlyData = Array(6).fill(0)
        const monthLabels = Array(6).fill('')
        
        for (let i = 5; i >= 0; i--) {
          const month = new Date(now.getFullYear(), now.getMonth() - i, 1)
          monthLabels[5-i] = month.toLocaleString(isRTL ? 'ar' : 'en', { month: 'short' })
          // Mock growth data - in real app, you'd calculate actual user registrations per month
          monthlyData[5-i] = Math.max(1, totalUsers - (i * 5))
        }

        setChartData(prev => ({
          ...prev,
          userGrowth: {
            labels: monthLabels,
            datasets: [{
              label: t('totalUsers'),
              data: monthlyData,
              borderColor: '#3b82f6',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              tension: 0.4,
            }]
          }
        }))

      } catch (error) {
        console.error("Error fetching admin data:", error)
      } finally {
        setLoading(false)
      }
    }

    if (user?.role === 'admin') {
      fetchAdminData()
    }
  }, [user, t, isRTL])

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Welcome Section */}
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent mb-2">
          {t('adminDashboard')}
        </h1>
        <p className="text-muted-foreground text-lg">
          {t('welcomeBack')}, {user?.name || user?.username}!
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Link href="/dashboard/ai">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-red-50 to-orange-100 dark:from-red-950/20 dark:to-orange-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-red-700 dark:text-red-300">{t('aiTraining')}</CardTitle>
              <div className="rounded-full bg-red-100 dark:bg-red-900/30 p-2">
                <Brain className="h-5 w-5 text-red-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-red-600 dark:text-red-400">{t('aiTrainingDesc')}</CardDescription>
            </CardContent>
          </Card>
        </Link>
        
        <Link href="/dashboard/admin">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/20 dark:to-indigo-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-blue-700 dark:text-blue-300">{t('userManagement')}</CardTitle>
              <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-2">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-blue-600 dark:text-blue-400">Manage users and permissions</CardDescription>
            </CardContent>
          </Card>
        </Link>
        
        <Link href="/dashboard/review">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-950/20 dark:to-emerald-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-green-700 dark:text-green-300">Audio Management</CardTitle>
              <div className="rounded-full bg-green-100 dark:bg-green-900/30 p-2">
                <FileAudio className="h-5 w-5 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-green-600 dark:text-green-400">Review and manage audio files</CardDescription>
            </CardContent>
          </Card>
        </Link>
        
        <Link href="/dashboard/ai">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-950/20 dark:to-violet-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-purple-700 dark:text-purple-300">{t('systemStats')}</CardTitle>
              <div className="rounded-full bg-purple-100 dark:bg-purple-900/30 p-2">
                <Server className="h-5 w-5 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-purple-600 dark:text-purple-400">Monitor AI models and system performance</CardDescription>
            </CardContent>
          </Card>
        </Link>

        <Link href="/dashboard/bulk-upload">
          <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 cursor-pointer border-0 bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-950/20 dark:to-orange-950/20">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-yellow-700 dark:text-yellow-300">{t('bulkUpload')}</CardTitle>
              <div className="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-2">
                <Upload className="h-5 w-5 text-yellow-600" />
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-yellow-600 dark:text-yellow-400">{t('bulkUploadDesc')}</CardDescription>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* System Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6">
        <Card className="border-0 bg-gradient-to-br from-blue-50 to-cyan-100 dark:from-blue-950/20 dark:to-cyan-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">{t('totalUsers')}</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-800 dark:text-blue-200">{systemStats.totalUsers}</div>
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {systemStats.activeUsers} active this month
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-0 bg-gradient-to-br from-green-50 to-teal-100 dark:from-green-950/20 dark:to-teal-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-700 dark:text-green-300">{t('totalAudio')}</CardTitle>
            <FileAudio className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-800 dark:text-green-200">{systemStats.totalAudio}</div>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              {systemStats.totalMinutes} total minutes
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-0 bg-gradient-to-br from-orange-50 to-amber-100 dark:from-orange-950/20 dark:to-amber-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Approval Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-800 dark:text-orange-200">
              {systemStats.totalAudio > 0 ? Math.round((systemStats.approvedAudio / systemStats.totalAudio) * 100) : 0}%
            </div>
            <Progress 
              value={systemStats.totalAudio > 0 ? (systemStats.approvedAudio / systemStats.totalAudio) * 100 : 0} 
              className="mt-2" 
            />
          </CardContent>
        </Card>
        
        <Card className="border-0 bg-gradient-to-br from-purple-50 to-pink-100 dark:from-purple-950/20 dark:to-pink-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">{t('systemHealth')}</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-800 dark:text-purple-200">
              {typeof systemStats.systemHealth === 'number' ? `${systemStats.systemHealth}%` : systemStats.systemHealth}
            </div>
            <Progress
              value={typeof systemStats.systemHealth === 'number' ? systemStats.systemHealth : 0}
              className={`mt-2 ${
                typeof systemStats.systemHealth === 'number'
                  ? systemStats.systemHealth >= 80
                    ? 'text-green-600'
                    : systemStats.systemHealth >= 60
                    ? 'text-yellow-600'
                    : 'text-red-600'
                  : 'text-gray-400'
              }`}
            />
            {typeof systemStats.systemHealth === 'number' && (
              <p className="text-xs text-muted-foreground mt-1">
                {systemStats.systemHealth >= 80 ? '🟢 Excellent' :
                 systemStats.systemHealth >= 60 ? '🟡 Good' :
                 systemStats.systemHealth >= 40 ? '🟠 Fair' : '🔴 Poor'}
              </p>
            )}
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950/20 dark:to-green-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Verified Users</CardTitle>
            <Shield className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-800 dark:text-emerald-200">{systemStats.verifiedUsers}</div>
            <p className="text-xs text-emerald-600 dark:text-emerald-400 mt-1">
              {systemStats.totalUsers > 0 ? Math.round((systemStats.verifiedUsers / systemStats.totalUsers) * 100) : 0}% verified
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-indigo-50 to-blue-100 dark:from-indigo-950/20 dark:to-blue-950/20">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-indigo-700 dark:text-indigo-300">New Users</CardTitle>
            <UserPlus className="h-4 w-4 text-indigo-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-800 dark:text-indigo-200">{systemStats.newUsersThisMonth}</div>
            <p className="text-xs text-indigo-600 dark:text-indigo-400 mt-1">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              User Growth
            </CardTitle>
            <CardDescription>User registration over time</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <Line
                data={chartData.userGrowth}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: {
                        stepSize: 1,
                      },
                    },
                  },
                }}
              />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileAudio className="h-5 w-5" />
              Audio Status Distribution
            </CardTitle>
            <CardDescription>Breakdown of audio files by status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <Bar
                data={chartData.audioStats}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      ticks: {
                        stepSize: 1,
                      },
                    },
                  },
                }}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>Latest system activity and uploads</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.length > 0 ? (
              recentActivity.map((activity) => {
                const timestamp = activity.timestamp?.toDate ? activity.timestamp.toDate() : new Date(activity.timestamp || 0)
                const displayUser = activity.user || activity.username || 'Unknown'
                const displayTitle = activity.description || activity.title || 'Unknown activity'
                const activityStatus = activity.metadata?.status || activity.status || 'pending'

                return (
                  <div key={activity.id} className="flex items-center justify-between p-4 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                    <div className="flex items-center gap-3">
                      <div className={`rounded-full p-2 ${
                        activity.type === 'user_registration'
                          ? 'bg-green-100 dark:bg-green-900/30'
                          : 'bg-blue-100 dark:bg-blue-900/30'
                      }`}>
                        {activity.type === 'user_registration' ? (
                          <UserPlus className="h-4 w-4 text-green-600" />
                        ) : (
                          <FileAudio className="h-4 w-4 text-blue-600" />
                        )}
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">{displayTitle}</p>
                        <p className="text-xs text-muted-foreground">
                          by {displayUser} • {timestamp.toLocaleDateString(isRTL ? 'ar' : 'en')} at {timestamp.toLocaleTimeString(isRTL ? 'ar' : 'en', { hour: '2-digit', minute: '2-digit' })}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {activity.type !== 'user_registration' && (
                        <Badge
                          variant={activityStatus === 'approved' ? 'default' : activityStatus === 'pending' ? 'secondary' : 'destructive'}
                          className="text-xs"
                        >
                          {activityStatus === 'approved' ? t('approved') : activityStatus === 'pending' ? t('pending') : t('rejected')}
                        </Badge>
                      )}
                      {activity.type === 'user_registration' && (
                        <Badge
                          variant={activity.metadata?.verified ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {activity.metadata?.verified ? 'Verified' : 'Unverified'}
                        </Badge>
                      )}
                    </div>
                  </div>
                )
              })
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent activity</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
