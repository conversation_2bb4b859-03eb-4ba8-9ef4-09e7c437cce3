import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'
import { collection, query, where, getDocs, orderBy, doc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { listUserAudio } from '@/lib/firebase-service'

// GET /api/users/[userId]/statistics - Get detailed user statistics
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params

    // Users can view their own stats, admins can view any stats
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Get user's audio recordings
    const userAudio = await listUserAudio(userId, { limit: 1000 })

    // Calculate comprehensive statistics
    const now = new Date()
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    // Basic stats
    const totalUploads = userAudio.length
    const approvedUploads = userAudio.filter(a => a.review?.status?.action === 'approved').length
    const pendingUploads = userAudio.filter(a => (a.review?.status?.action || 'pending') === 'pending').length
    const rejectedUploads = userAudio.filter(a => a.review?.status?.action === 'rejected').length

    // Duration stats
    const totalDuration = userAudio.reduce((acc, a) => acc + (a.duration || 0), 0)
    const averageDuration = totalUploads > 0 ? totalDuration / totalUploads : 0
    const approvedDuration = userAudio
      .filter(a => a.review?.status?.action === 'approved')
      .reduce((acc, a) => acc + (a.duration || 0), 0)

    // Recent activity stats
    const recentUploads = userAudio.filter(a => {
      const uploadDate = new Date(a.created_at || 0)
      return uploadDate >= thirtyDaysAgo
    })

    const weeklyUploads = userAudio.filter(a => {
      const uploadDate = new Date(a.created_at || 0)
      return uploadDate >= sevenDaysAgo
    })

    // Quality metrics
    const approvalRate = totalUploads > 0 ? (approvedUploads / totalUploads) * 100 : 0
    const rejectionRate = totalUploads > 0 ? (rejectedUploads / totalUploads) * 100 : 0

    // Activity timeline (last 12 months)
    const monthlyStats = []
    for (let i = 11; i >= 0; i--) {
      const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
      
      const monthUploads = userAudio.filter(a => {
        const uploadDate = new Date(a.created_at || 0)
        return uploadDate >= monthStart && uploadDate <= monthEnd
      })

      monthlyStats.push({
        month: monthStart.toISOString().slice(0, 7), // YYYY-MM format
        uploads: monthUploads.length,
        approved: monthUploads.filter(a => a.review?.status?.action === 'approved').length,
        duration: monthUploads.reduce((acc, a) => acc + (a.duration || 0), 0)
      })
    }

    // Language distribution (if available in transcriptions)
    const languageStats = {}
    userAudio.forEach(audio => {
      const language = audio.transcriptions?.primary?.language || 'unknown'
      languageStats[language] = (languageStats[language] || 0) + 1
    })

    // Top performing uploads (by duration and approval)
    const topUploads = userAudio
      .filter(a => a.review?.status?.action === 'approved')
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, 5)
      .map(audio => ({
        id: audio.id,
        title: audio.transcriptions?.primary?.text?.slice(0, 50) + '...' || 'Untitled',
        duration: audio.duration,
        created_at: audio.created_at,
        status: audio.review?.status?.action
      }))

    // Streak calculation (consecutive days with uploads)
    const uploadDates = userAudio
      .map(a => new Date(a.created_at || 0).toDateString())
      .filter((date, index, arr) => arr.indexOf(date) === index)
      .sort()

    let currentStreak = 0
    let longestStreak = 0
    let tempStreak = 1

    for (let i = uploadDates.length - 1; i > 0; i--) {
      const current = new Date(uploadDates[i])
      const previous = new Date(uploadDates[i - 1])
      const diffDays = Math.floor((current.getTime() - previous.getTime()) / (1000 * 60 * 60 * 24))

      if (diffDays === 1) {
        tempStreak++
        if (i === uploadDates.length - 1) {
          currentStreak = tempStreak
        }
      } else {
        longestStreak = Math.max(longestStreak, tempStreak)
        tempStreak = 1
        if (i === uploadDates.length - 1) {
          currentStreak = 1
        }
      }
    }
    longestStreak = Math.max(longestStreak, tempStreak)

    const statistics = {
      overview: {
        totalUploads,
        approvedUploads,
        pendingUploads,
        rejectedUploads,
        totalDuration: Math.round(totalDuration),
        totalMinutes: Math.round(totalDuration / 60),
        averageDuration: Math.round(averageDuration),
        approvedDuration: Math.round(approvedDuration),
        approvedMinutes: Math.round(approvedDuration / 60)
      },
      quality: {
        approvalRate: Math.round(approvalRate * 100) / 100,
        rejectionRate: Math.round(rejectionRate * 100) / 100,
        qualityScore: Math.round((approvalRate - rejectionRate) * 100) / 100
      },
      activity: {
        recentUploads: recentUploads.length,
        weeklyUploads: weeklyUploads.length,
        currentStreak,
        longestStreak,
        activeDays: uploadDates.length
      },
      timeline: monthlyStats,
      languages: languageStats,
      topUploads,
      lastActivity: userAudio.length > 0 ? userAudio[0]?.created_at : null
    }

    return NextResponse.json(statistics)
  } catch (error) {
    console.error('Error fetching user statistics:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
