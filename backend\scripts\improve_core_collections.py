#!/usr/bin/env python3
"""
Improve Core Collections Structure

Improves audio, transcription, and users collections for better organization,
performance, and scalability.

CURRENT ISSUES:
- Audio: Flat structure with mixed concerns
- Transcription: Redundant data, separate collection
- Users: Basic structure, no organization

NEW IMPROVED STRUCTURE:
- audio/{audio_id}/* (hierarchical with subcollections)
- users/{user_id}/* (hierarchical with subcollections)
- content/{content_id}/* (unified content management)
"""

import sys
import os
from datetime import datetime
from typing import Dict, Any, List

# Add backend to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.firebase_clean import clean_firebase_service
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CoreCollectionsImprover:
    def __init__(self):
        self.db = None
        self.improvement_log = []
        
    def initialize(self):
        """Initialize Firebase connection"""
        try:
            clean_firebase_service.initialize()
            self.db = clean_firebase_service.db
            logger.info("✅ Firebase initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Firebase: {e}")
            raise
    
    def log_improvement(self, action: str, details: str):
        """Log improvement actions"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "details": details
        }
        self.improvement_log.append(log_entry)
        logger.info(f"📝 {action}: {details}")
    
    def improve_audio_structure(self):
        """Improve audio collection with hierarchical structure"""
        logger.info("🔄 Improving audio collection structure...")
        
        try:
            # Get ALL existing audio documents
            audio_docs = self.db.collection('audio').get()  # Get all audio records
            
            for doc in audio_docs:
                audio_id = doc.id
                data = doc.to_dict()
                
                # Create new hierarchical structure (clean name without _v2)
                audio_ref = self.db.collection('audio_new').document(audio_id)
                
                # Main audio document (core metadata only)
                main_data = {
                    'id': audio_id,
                    'title': data.get('title'),
                    'audio_url': data.get('audio_url'),
                    'duration': data.get('duration'),
                    'format': data.get('format'),
                    'created_at': data.get('created_at'),
                    'updated_at': datetime.now().isoformat(),
                    'user_id': data.get('user_id'),
                    'source': data.get('source'),
                    'migrated_from': f'audio/{audio_id}'
                }
                audio_ref.set(main_data)
                
                # Metadata subcollection
                metadata_data = {
                    'gender': data.get('gender'),
                    'language': data.get('language', 'masalit'),
                    'dialect': data.get('dialect'),
                    'quality_rating': data.get('quality_rating'),
                    'recording_context': data.get('recording_context')
                }
                audio_ref.collection('metadata').document('details').set(metadata_data)
                
                # Review subcollection
                review_data = {
                    'action': data.get('action', 'pending'),
                    'reviewed_by': data.get('reviewed_by'),
                    'reviewed_at': data.get('reviewed_at'),
                    'feedback': data.get('feedback'),
                    'is_flagged': data.get('is_flagged', False)
                }
                audio_ref.collection('review').document('status').set(review_data)
                
                # Training subcollection - Single source of truth for audio+transcription training
                training_data = {
                    'trained_asr': data.get('trained_asr', False),
                    'tts_trained': data.get('tts_trained', False),
                    'training_epoch': data.get('training_epoch'),
                    'last_trained': data.get('last_trained'),
                    'last_trained_at': data.get('last_trained_at'),
                    'training_sessions': [],  # List of training session IDs
                    'note': 'Audio and transcription training status are unified - if audio is trained, transcription is also trained'
                }
                audio_ref.collection('training').document('status').set(training_data)
                
                self.log_improvement("IMPROVE_AUDIO", f"audio/{audio_id} -> audio_new/{audio_id}/*")
                
        except Exception as e:
            logger.error(f"❌ Error improving audio structure: {e}")
    
    def improve_transcription_structure(self):
        """Integrate transcriptions into audio hierarchy"""
        logger.info("🔄 Improving transcription structure...")
        
        try:
            # Get ALL existing transcription documents
            transcription_docs = self.db.collection('transcription').get()
            
            for doc in transcription_docs:
                transcription_id = doc.id
                data = doc.to_dict()
                audio_id = data.get('audio_id')
                
                if audio_id:
                    # Add transcription as subcollection of audio
                    audio_ref = self.db.collection('audio_new').document(audio_id)
                    
                    transcription_data = {
                        'content': data.get('content'),
                        'language': data.get('language'),
                        'dialect': data.get('dialect'),
                        'transcription_source': data.get('transcription_source'),
                        'type': data.get('type'),
                        'speaker_count': data.get('speaker_count', 1),
                        'quality_rating': data.get('quality_rating'),
                        'notes': data.get('notes'),
                        'created_at': data.get('created_at'),
                        'updated_at': data.get('updated_at'),
                        'migrated_from': f'transcription/{transcription_id}',
                        'note': 'Training status is managed at audio level - transcription inherits training status from parent audio'
                    }
                    
                    audio_ref.collection('transcriptions').document('primary').set(transcription_data)
                    
                    self.log_improvement("IMPROVE_TRANSCRIPTION", f"transcription/{transcription_id} -> audio_new/{audio_id}/transcriptions/primary")
                
        except Exception as e:
            logger.error(f"❌ Error improving transcription structure: {e}")
    
    def improve_users_structure(self):
        """Improve users collection with hierarchical structure"""
        logger.info("🔄 Improving users collection structure...")
        
        try:
            # Get ALL existing user documents
            user_docs = self.db.collection('users').get()
            
            for doc in user_docs:
                user_id = doc.id
                data = doc.to_dict()
                
                # Create new hierarchical structure (clean name without _v2)
                user_ref = self.db.collection('users_new').document(user_id)
                
                # Main user document (core info only)
                main_data = {
                    'email': data.get('email'),
                    'name': data.get('name'),
                    'username': data.get('username'),
                    'role': data.get('role', 'user'),
                    'created_at': data.get('created_at'),
                    'updated_at': datetime.now().isoformat(),
                    'migrated_from': f'users/{user_id}'
                }
                user_ref.set(main_data)
                
                # Profile subcollection
                profile_data = {
                    'avatar_url': data.get('avatar_url'),
                    'bio': data.get('bio'),
                    'location': data.get('location'),
                    'language_preferences': data.get('language_preferences', ['masalit']),
                    'timezone': data.get('timezone'),
                    'email_verified': data.get('emailVerified', False),
                    'phone_verified': data.get('phoneVerified', False)
                }
                user_ref.collection('profile').document('details').set(profile_data)
                
                # Statistics subcollection
                stats_data = {
                    'contribution_count': data.get('contribution_count', 0),
                    'audio_uploads': 0,
                    'transcriptions_created': 0,
                    'reviews_completed': 0,
                    'training_sessions': 0,
                    'total_audio_duration': 0,
                    'last_activity': data.get('updated_at')
                }
                user_ref.collection('statistics').document('summary').set(stats_data)
                
                # Preferences subcollection
                preferences_data = {
                    'theme': 'light',
                    'language': 'en',
                    'notifications': {
                        'email': True,
                        'push': True,
                        'training_updates': True,
                        'review_notifications': True
                    },
                    'privacy': {
                        'profile_public': False,
                        'stats_public': False,
                        'allow_contact': True
                    }
                }
                user_ref.collection('preferences').document('settings').set(preferences_data)
                
                # Security subcollection
                security_data = {
                    'last_login': data.get('last_login'),
                    'login_count': data.get('login_count', 0),
                    'failed_login_attempts': 0,
                    'account_locked': False,
                    'two_factor_enabled': False,
                    'registration_details': data.get('registration_details', {}),
                    'is_disabled': data.get('isDisabled', False)
                }
                user_ref.collection('security').document('status').set(security_data)
                
                self.log_improvement("IMPROVE_USERS", f"users/{user_id} -> users_new/{user_id}/*")
                
        except Exception as e:
            logger.error(f"❌ Error improving users structure: {e}")
    
    def create_content_management_structure(self):
        """Create unified content management structure"""
        logger.info("🔄 Creating content management structure...")
        
        try:
            # Create content management collections
            content_types = ['audio', 'text', 'mixed']
            
            for content_type in content_types:
                # Create content type configuration
                config_ref = self.db.collection('content').document('config').collection('types').document(content_type)
                
                config_data = {
                    'type': content_type,
                    'allowed_formats': self._get_allowed_formats(content_type),
                    'max_size_mb': self._get_max_size(content_type),
                    'processing_pipeline': self._get_processing_pipeline(content_type),
                    'quality_requirements': self._get_quality_requirements(content_type),
                    'created_at': datetime.now().isoformat()
                }
                
                config_ref.set(config_data)
                self.log_improvement("CREATE_CONTENT_CONFIG", f"Created content type config: {content_type}")
            
            # Create content workflow states
            workflow_states = ['uploaded', 'processing', 'review', 'approved', 'rejected', 'training', 'completed']
            
            for state in workflow_states:
                state_ref = self.db.collection('content').document('workflow').collection('states').document(state)
                
                state_data = {
                    'state': state,
                    'description': f'Content in {state} state',
                    'next_states': self._get_next_states(state),
                    'required_permissions': self._get_required_permissions(state),
                    'created_at': datetime.now().isoformat()
                }
                
                state_ref.set(state_data)
                self.log_improvement("CREATE_WORKFLOW_STATE", f"Created workflow state: {state}")
                
        except Exception as e:
            logger.error(f"❌ Error creating content management structure: {e}")
    
    def _get_allowed_formats(self, content_type: str) -> List[str]:
        """Get allowed formats for content type"""
        formats = {
            'audio': ['wav', 'mp3', 'ogg', 'webm'],
            'text': ['txt', 'pdf', 'docx', 'json'],
            'mixed': ['wav', 'mp3', 'txt', 'pdf']
        }
        return formats.get(content_type, [])
    
    def _get_max_size(self, content_type: str) -> int:
        """Get max size for content type"""
        sizes = {
            'audio': 100,  # MB
            'text': 10,    # MB
            'mixed': 100   # MB
        }
        return sizes.get(content_type, 50)
    
    def _get_processing_pipeline(self, content_type: str) -> List[str]:
        """Get processing pipeline for content type"""
        pipelines = {
            'audio': ['upload', 'format_validation', 'quality_check', 'transcription', 'review'],
            'text': ['upload', 'format_validation', 'language_detection', 'quality_check', 'review'],
            'mixed': ['upload', 'format_validation', 'audio_processing', 'text_processing', 'alignment', 'review']
        }
        return pipelines.get(content_type, [])
    
    def _get_quality_requirements(self, content_type: str) -> Dict[str, Any]:
        """Get quality requirements for content type"""
        requirements = {
            'audio': {
                'min_duration_seconds': 1,
                'max_duration_seconds': 300,
                'min_sample_rate': 16000,
                'max_noise_level': 0.1
            },
            'text': {
                'min_length_chars': 10,
                'max_length_chars': 10000,
                'required_language': 'masalit'
            },
            'mixed': {
                'audio_text_alignment': 0.8,
                'min_confidence': 0.7
            }
        }
        return requirements.get(content_type, {})
    
    def _get_next_states(self, state: str) -> List[str]:
        """Get possible next states"""
        transitions = {
            'uploaded': ['processing', 'rejected'],
            'processing': ['review', 'rejected'],
            'review': ['approved', 'rejected'],
            'approved': ['training'],
            'rejected': ['uploaded'],
            'training': ['completed'],
            'completed': []
        }
        return transitions.get(state, [])
    
    def _get_required_permissions(self, state: str) -> List[str]:
        """Get required permissions for state"""
        permissions = {
            'uploaded': ['user'],
            'processing': ['system'],
            'review': ['reviewer', 'admin'],
            'approved': ['reviewer', 'admin'],
            'rejected': ['reviewer', 'admin'],
            'training': ['system', 'admin'],
            'completed': ['system']
        }
        return permissions.get(state, [])
    
    def create_backup(self):
        """Create backup before improvement"""
        logger.info("💾 Creating backup of core collections...")
        
        backup_data = {
            'backup_created_at': datetime.now().isoformat(),
            'collections_to_improve': ['audio', 'transcription', 'users'],
            'improvement_type': 'core_collections_hierarchical_restructure'
        }
        
        self.db.collection('_improvement_backup').document('core_collections_backup').set(backup_data)
        self.log_improvement("BACKUP", "Created backup record for core collections improvement")
    
    def save_improvement_log(self):
        """Save improvement log"""
        try:
            log_doc = {
                'improvement_completed_at': datetime.now().isoformat(),
                'total_actions': len(self.improvement_log),
                'actions': self.improvement_log
            }
            
            clean_firebase_service.add_system_log('system', {
                'level': 'INFO',
                'message': 'Core collections improvement completed',
                'source': 'core_collections_improver',
                'details': log_doc
            })
            
            logger.info(f"✅ Improvement log saved with {len(self.improvement_log)} actions")
            
        except Exception as e:
            logger.error(f"❌ Error saving improvement log: {e}")
    
    def run_improvement(self, create_backup: bool = True):
        """Run the complete core collections improvement"""
        try:
            self.initialize()
            
            if create_backup:
                self.create_backup()
            
            logger.info("🚀 Starting core collections improvement...")
            
            # Run improvements
            self.improve_audio_structure()
            self.improve_transcription_structure()
            self.improve_users_structure()
            self.create_content_management_structure()
            
            # Save log
            self.save_improvement_log()
            
            logger.info("✅ Core collections improvement completed successfully!")
            logger.info(f"📊 Total actions performed: {len(self.improvement_log)}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Core collections improvement failed: {e}")
            return False

def main():
    """Main improvement function"""
    improver = CoreCollectionsImprover()
    
    print("🔄 Core Collections Structure Improvement")
    print("=" * 50)
    print("This will improve audio, transcription, and users collections")
    print("with hierarchical structure for better organization and performance.")
    print()
    
    confirm = input("Do you want to proceed? (y/N): ").lower().strip()
    
    if confirm == 'y':
        success = improver.run_improvement()
        if success:
            print("\n✅ Core collections improvement completed successfully!")
            print("📊 New structure:")
            print("   - audio_v2/{id}/* (hierarchical audio data)")
            print("   - users_v2/{id}/* (hierarchical user data)")
            print("   - content/* (unified content management)")
        else:
            print("\n❌ Core collections improvement failed. Check logs for details.")
    else:
        print("Core collections improvement cancelled.")

if __name__ == "__main__":
    main()
