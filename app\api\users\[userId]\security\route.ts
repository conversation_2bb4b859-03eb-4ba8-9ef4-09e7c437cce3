import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth } from '@/lib/firebase-admin'
import { doc, getDoc, setDoc, updateDoc, collection, query, where, orderBy, limit, getDocs, addDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface SecuritySettings {
  two_factor_enabled: boolean
  two_factor_method: 'sms' | 'email' | 'authenticator' | null
  login_notifications: boolean
  suspicious_activity_alerts: boolean
  device_management_enabled: boolean
  session_timeout: number // in minutes
  password_last_changed: string
  security_questions: {
    question: string
    answer_hash: string
  }[]
  trusted_devices: string[]
  blocked_ips: string[]
  created_at: string
  updated_at: string
}

interface LoginSession {
  id?: string
  userId: string
  session_id: string
  ip_address: string
  user_agent: string
  device_info: {
    browser: string
    os: string
    device_type: 'desktop' | 'mobile' | 'tablet'
    is_trusted: boolean
  }
  location?: {
    country: string
    city: string
    region: string
  }
  login_time: string
  last_activity: string
  is_active: boolean
  logout_time?: string
  logout_reason?: 'manual' | 'timeout' | 'security' | 'admin'
}

interface SecurityEvent {
  id?: string
  userId: string
  event_type: 'login_success' | 'login_failed' | 'password_change' | 'suspicious_activity' | 'device_added' | 'device_removed' | 'security_settings_changed'
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  ip_address: string
  user_agent: string
  metadata: any
  timestamp: string
  resolved: boolean
  resolved_by?: string
  resolved_at?: string
}

// GET /api/users/[userId]/security - Get user security settings and history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params

    // Users can view their own security info, admins can view any
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const include = searchParams.get('include')?.split(',') || ['settings']

    const result: any = {}

    // Get security settings
    if (include.includes('settings')) {
      try {
        const securityRef = doc(db, 'users', userId, 'security', 'settings')
        const securityDoc = await getDoc(securityRef)

        if (securityDoc.exists()) {
          result.settings = securityDoc.data()
        } else {
          // Create default security settings
          const defaultSettings: SecuritySettings = {
            two_factor_enabled: false,
            two_factor_method: null,
            login_notifications: true,
            suspicious_activity_alerts: true,
            device_management_enabled: true,
            session_timeout: 60, // 1 hour
            password_last_changed: new Date().toISOString(),
            security_questions: [],
            trusted_devices: [],
            blocked_ips: [],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          try {
            await setDoc(securityRef, defaultSettings)
          } catch (setError) {
            console.error('Error creating default security settings:', setError)
          }
          result.settings = defaultSettings
        }
      } catch (error) {
        console.error('Error fetching security settings:', error)
        // Provide fallback default settings
        result.settings = {
          two_factor_enabled: false,
          two_factor_method: null,
          login_notifications: true,
          suspicious_activity_alerts: true,
          device_management_enabled: true,
          session_timeout: 60,
          password_last_changed: new Date().toISOString(),
          security_questions: [],
          trusted_devices: [],
          blocked_ips: [],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      }
    }

    // Get login history (simplified query to avoid index requirements)
    if (include.includes('sessions')) {
      try {
        // Query user's sessions subcollection
        const sessionsQuery = query(
          collection(db, 'users', userId, 'sessions'),
          limit(50)
        )
        const sessionsSnapshot = await getDocs(sessionsQuery)
        const sessions = sessionsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))

        // Sort client-side by login_time
        result.sessions = sessions.sort((a, b) => {
          const timeA = new Date(a.login_time || 0).getTime()
          const timeB = new Date(b.login_time || 0).getTime()
          return timeB - timeA // Descending order
        })
      } catch (error) {
        console.error('Error fetching sessions:', error)
        // Provide mock session data for demonstration
        result.sessions = [{
          id: 'current_session',
          session_id: 'current',
          ip_address: '127.0.0.1',
          user_agent: 'Current Browser Session',
          device_info: {
            browser: 'Chrome',
            os: 'Windows',
            device_type: 'desktop',
            is_trusted: true
          },
          login_time: new Date().toISOString(),
          last_activity: new Date().toISOString(),
          is_active: true
        }]
      }
    }

    // Get security events (simplified query)
    if (include.includes('events')) {
      try {
        // Query user's security events subcollection
        const eventsQuery = query(
          collection(db, 'users', userId, 'security_events'),
          limit(100)
        )
        const eventsSnapshot = await getDocs(eventsQuery)
        const events = eventsSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }))

        // Sort client-side by timestamp
        result.events = events.sort((a, b) => {
          const timeA = new Date(a.timestamp || 0).getTime()
          const timeB = new Date(b.timestamp || 0).getTime()
          return timeB - timeA // Descending order
        })
      } catch (error) {
        console.error('Error fetching security events:', error)
        result.events = []
      }
    }

    // Get active sessions count (simplified query)
    if (include.includes('active_sessions')) {
      try {
        // Get all user sessions and filter client-side
        const sessionsQuery = query(
          collection(db, 'user_sessions'),
          where('userId', '==', userId)
        )
        const sessionsSnapshot = await getDocs(sessionsQuery)
        const activeSessions = sessionsSnapshot.docs.filter(doc => {
          const data = doc.data()
          return data.is_active === true
        })
        result.active_sessions_count = activeSessions.length
      } catch (error) {
        console.error('Error fetching active sessions count:', error)
        result.active_sessions_count = 0
      }
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching security info:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/users/[userId]/security - Update security settings
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params
    const updates = await request.json()

    // Users can update their own security settings, admins can update any
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Validate updates
    const allowedUpdates = [
      'two_factor_enabled', 'two_factor_method', 'login_notifications',
      'suspicious_activity_alerts', 'device_management_enabled', 'session_timeout'
    ]
    const updateKeys = Object.keys(updates)
    const invalidKeys = updateKeys.filter(key => !allowedUpdates.includes(key))
    
    if (invalidKeys.length > 0) {
      return NextResponse.json({ 
        error: `Invalid security setting fields: ${invalidKeys.join(', ')}` 
      }, { status: 400 })
    }

    // Get current settings
    const securityRef = doc(db, 'users', userId, 'security', 'settings')
    const securityDoc = await getDoc(securityRef)
    
    let currentSettings: SecuritySettings
    if (securityDoc.exists()) {
      currentSettings = securityDoc.data() as SecuritySettings
    } else {
      return NextResponse.json({ error: 'Security settings not found' }, { status: 404 })
    }

    // Merge updates
    const updatedSettings = {
      ...currentSettings,
      ...updates,
      updated_at: new Date().toISOString()
    }

    // Update security settings
    await setDoc(securityRef, updatedSettings)

    // Log security settings change
    await addDoc(collection(db, 'security_events'), {
      userId,
      event_type: 'security_settings_changed',
      description: `Security settings updated: ${Object.keys(updates).join(', ')}`,
      severity: 'medium',
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      metadata: { changes: updates },
      timestamp: new Date().toISOString(),
      resolved: true
    })

    return NextResponse.json({
      message: 'Security settings updated successfully',
      settings: updatedSettings
    })
  } catch (error) {
    console.error('Error updating security settings:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/users/[userId]/security - Create security event or session
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string }> }
) {
  try {
    // Get session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify the session cookie
    const decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    const currentUserId = decodedClaims.uid

    // Get current user data to check role
    const currentUserDoc = await getDoc(doc(db, 'users', currentUserId))
    if (!currentUserDoc.exists()) {
      return NextResponse.json({ error: 'Current user not found' }, { status: 404 })
    }
    const currentUserData = currentUserDoc.data()

    const { userId } = await params
    const { action, data } = await request.json()

    // Users can create events for themselves, admins can create for any user
    if (currentUserId !== userId && currentUserData.role !== 'admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    switch (action) {
      case 'create_session':
        const sessionData: LoginSession = {
          userId,
          session_id: data.session_id,
          ip_address: data.ip_address || request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: data.user_agent || request.headers.get('user-agent') || 'unknown',
          device_info: data.device_info,
          location: data.location,
          login_time: new Date().toISOString(),
          last_activity: new Date().toISOString(),
          is_active: true
        }
        
        const sessionRef = await addDoc(collection(db, 'users', userId, 'sessions'), sessionData)
        return NextResponse.json({ 
          message: 'Session created successfully',
          session_id: sessionRef.id 
        })

      case 'end_session':
        // End a specific session
        const sessionToEnd = doc(db, 'users', userId, 'sessions', data.session_id)
        await updateDoc(sessionToEnd, {
          is_active: false,
          logout_time: new Date().toISOString(),
          logout_reason: data.reason || 'manual'
        })
        
        return NextResponse.json({ message: 'Session ended successfully' })

      case 'create_security_event':
        const eventData: SecurityEvent = {
          userId,
          event_type: data.event_type,
          description: data.description,
          severity: data.severity || 'medium',
          ip_address: data.ip_address || request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: data.user_agent || request.headers.get('user-agent') || 'unknown',
          metadata: data.metadata || {},
          timestamp: new Date().toISOString(),
          resolved: false
        }
        
        const eventRef = await addDoc(collection(db, 'users', userId, 'security_events'), eventData)
        return NextResponse.json({ 
          message: 'Security event created successfully',
          event_id: eventRef.id 
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }
  } catch (error) {
    console.error('Error creating security record:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
