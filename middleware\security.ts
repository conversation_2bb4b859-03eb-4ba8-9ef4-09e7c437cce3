import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { securityService } from '@/lib/security-service'

interface SecurityMiddlewareOptions {
  trackAccess?: boolean
  requireAuth?: boolean
  adminOnly?: boolean
  rateLimitKey?: string
  rateLimitMax?: number
  rateLimitWindow?: number // in minutes
}

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function withSecurity(
  handler: (req: NextRequest, ...args: any[]) => Promise<NextResponse>,
  options: SecurityMiddlewareOptions = {}
) {
  return async function (req: NextRequest, ...args: any[]): Promise<NextResponse> {
    const startTime = Date.now()
    
    try {
      // Get client information
      const ip_address = req.headers.get('x-forwarded-for') || 
                        req.headers.get('x-real-ip') || 
                        'unknown'
      const user_agent = req.headers.get('user-agent') || 'unknown'

      // Rate limiting
      if (options.rateLimitKey) {
        const rateLimitResult = await checkRateLimit(
          options.rateLimitKey,
          ip_address,
          options.rateLimitMax || 100,
          options.rateLimitWindow || 15
        )
        
        if (!rateLimitResult.allowed) {
          return NextResponse.json(
            { error: 'Rate limit exceeded' },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Limit': options.rateLimitMax?.toString() || '100',
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': rateLimitResult.resetTime?.toString() || '0'
              }
            }
          )
        }
      }

      // Authentication check
      const session = await getServerSession(authOptions)
      
      if (options.requireAuth && !session) {
        // Track unauthorized access attempt
        await securityService.createSecurityEvent({
          userId: 'anonymous',
          event_type: 'unauthorized_access',
          description: `Unauthorized access attempt to ${req.url}`,
          severity: 'medium',
          ip_address,
          user_agent,
          metadata: {
            url: req.url,
            method: req.method,
            requiresAuth: true
          }
        })

        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
      }

      // Admin-only check
      if (options.adminOnly && session?.user?.role !== 'admin') {
        // Track unauthorized admin access attempt
        if (session?.user?.id) {
          await securityService.trackUnauthorizedAccess(
            session.user.id,
            req.url,
            ip_address,
            user_agent
          )
        }

        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }

      // Track access if enabled
      if (options.trackAccess && session?.user?.id) {
        await securityService.createSecurityEvent({
          userId: session.user.id,
          event_type: 'login_success', // Using as general access event
          description: `Accessed ${req.method} ${req.url}`,
          severity: 'low',
          ip_address,
          user_agent,
          metadata: {
            url: req.url,
            method: req.method,
            duration: Date.now() - startTime
          }
        })
      }

      // Execute the handler
      const response = await handler(req, ...args)

      // Track successful API calls for sensitive operations
      if (session?.user?.id && isSensitiveOperation(req.url, req.method)) {
        await securityService.createSecurityEvent({
          userId: session.user.id,
          event_type: 'security_settings_changed',
          description: `Performed ${req.method} operation on ${req.url}`,
          severity: 'medium',
          ip_address,
          user_agent,
          metadata: {
            url: req.url,
            method: req.method,
            status: response.status,
            duration: Date.now() - startTime
          }
        })
      }

      return response
    } catch (error) {
      // Track security errors
      const session = await getServerSession(authOptions)
      if (session?.user?.id) {
        await securityService.createSecurityEvent({
          userId: session.user.id,
          event_type: 'suspicious_activity',
          description: `API error on ${req.method} ${req.url}: ${error.message}`,
          severity: 'high',
          ip_address: req.headers.get('x-forwarded-for') || 'unknown',
          user_agent: req.headers.get('user-agent') || 'unknown',
          metadata: {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method
          }
        })
      }

      throw error
    }
  }
}

async function checkRateLimit(
  key: string,
  identifier: string,
  maxRequests: number,
  windowMinutes: number
): Promise<{ allowed: boolean; resetTime?: number }> {
  const now = Date.now()
  const windowMs = windowMinutes * 60 * 1000
  const rateLimitKey = `${key}:${identifier}`
  
  const current = rateLimitStore.get(rateLimitKey)
  
  if (!current || now > current.resetTime) {
    // New window or expired window
    const resetTime = now + windowMs
    rateLimitStore.set(rateLimitKey, { count: 1, resetTime })
    return { allowed: true, resetTime }
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, resetTime: current.resetTime }
  }
  
  // Increment count
  rateLimitStore.set(rateLimitKey, { 
    count: current.count + 1, 
    resetTime: current.resetTime 
  })
  
  return { allowed: true, resetTime: current.resetTime }
}

function isSensitiveOperation(url: string, method: string): boolean {
  const sensitivePatterns = [
    '/api/users/.*/security',
    '/api/users/.*/preferences',
    '/api/admin/',
    '/api/training/',
    '/api/asr/train'
  ]
  
  const sensitiveMethods = ['POST', 'PATCH', 'PUT', 'DELETE']
  
  return sensitiveMethods.includes(method) && 
         sensitivePatterns.some(pattern => new RegExp(pattern).test(url))
}

// Predefined middleware configurations
export const authRequired = (handler: any) => 
  withSecurity(handler, { requireAuth: true, trackAccess: true })

export const adminRequired = (handler: any) => 
  withSecurity(handler, { requireAuth: true, adminOnly: true, trackAccess: true })

export const rateLimited = (max: number, windowMinutes: number) => (handler: any) =>
  withSecurity(handler, { 
    rateLimitKey: 'api',
    rateLimitMax: max,
    rateLimitWindow: windowMinutes
  })

export const secureEndpoint = (handler: any) =>
  withSecurity(handler, {
    requireAuth: true,
    trackAccess: true,
    rateLimitKey: 'secure',
    rateLimitMax: 50,
    rateLimitWindow: 15
  })

// Security event helpers
export async function trackSuspiciousActivity(
  req: NextRequest,
  userId: string,
  description: string,
  metadata: any = {}
) {
  const ip_address = req.headers.get('x-forwarded-for') || 'unknown'
  const user_agent = req.headers.get('user-agent') || 'unknown'
  
  await securityService.trackSuspiciousActivity(
    userId,
    description,
    ip_address,
    user_agent,
    {
      ...metadata,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    }
  )
}

export async function trackUnauthorizedAccess(
  req: NextRequest,
  userId: string,
  resource: string
) {
  const ip_address = req.headers.get('x-forwarded-for') || 'unknown'
  const user_agent = req.headers.get('user-agent') || 'unknown'
  
  await securityService.trackUnauthorizedAccess(
    userId,
    resource,
    ip_address,
    user_agent
  )
}

// Clean up rate limit store periodically (in production, use a proper cache)
setInterval(() => {
  const now = Date.now()
  for (const [key, value] of rateLimitStore.entries()) {
    if (now > value.resetTime) {
      rateLimitStore.delete(key)
    }
  }
}, 5 * 60 * 1000) // Clean up every 5 minutes
