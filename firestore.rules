rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow reading users collection for login/authentication purposes
    match /users/{userId} {
      allow read: if true; // Allow reading for login
      allow write: if request.auth != null;
    }

    // Simple rule: authenticated users can read and write all other data
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}