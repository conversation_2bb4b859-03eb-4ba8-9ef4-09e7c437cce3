import { NextResponse } from "next/server"
import { storage, db } from "@/lib/firebase"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { collection, addDoc, updateDoc, increment, doc, setDoc } from "firebase/firestore"
import { createTranscription } from "@/lib/transcription"

export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const userId = formData.get('user_id') as string
    const metadata = JSON.parse(formData.get('metadata') as string)
    const audioFiles = formData.getAll('files') as File[]

    console.log('Bulk upload request:', {
      userId,
      metadataCount: metadata?.length,
      filesCount: audioFiles?.length
    })

    if (!userId || userId === 'undefined' || !metadata || !audioFiles.length) {
      return NextResponse.json(
        { error: 'Missing required fields', details: { userId: userId, metadata: !!metadata, files: audioFiles.length } },
        { status: 400 }
      )
    }

    const results = []
    const errors = []

    for (const file of audioFiles) {
      try {
        // Find metadata for this file
        const fileMetadata = metadata.find((m: any) => m.filename === file.name)
        if (!fileMetadata) {
          errors.push(`No metadata found for ${file.name}`)
          continue
        }

        // Create a unique file path
        const timestamp = Date.now()
        const fileExtension = file.name.split('.').pop()
        const fileName = `${userId}/${timestamp}_${file.name}`
        const storageRef = ref(storage, `audio/${fileName}`)

        // Upload the file
        const snapshot = await uploadBytes(storageRef, file)
        const downloadURL = await getDownloadURL(snapshot.ref)

        // Create hierarchical audio record
        const audioId = `audio_${timestamp}`
        const timestampISO = new Date().toISOString()

        // Create main audio document
        const audioRef = doc(db, 'audio', audioId)
        await setDoc(audioRef, {
          id: audioId,
          title: fileMetadata.title,
          audio_url: downloadURL,
          duration: fileMetadata.duration || 0,
          format: file.type,
          created_at: timestampISO,
          updated_at: timestampISO,
          user_id: userId,
          source: 'bulk_upload'
        })

        // Create transcription subcollection
        const transcriptionRef = doc(db, 'audio', audioId, 'transcriptions', 'primary')
        await setDoc(transcriptionRef, {
          content: fileMetadata.transcription,
          language: 'masalit',
          transcription_source: 'human/manual',
          type: 'txt',
          created_at: timestampISO,
          updated_at: timestampISO,
          speaker_count: 1
        })

        // Create metadata subcollection
        const metadataRef = doc(db, 'audio', audioId, 'metadata', 'details')
        await setDoc(metadataRef, {
          gender: fileMetadata.speaker,
          language: 'masalit',
          recording_context: 'bulk_upload',
          created_at: timestampISO,
          updated_at: timestampISO
        })

        // Create review subcollection with initial status
        const reviewRef = doc(db, 'audio', audioId, 'review', 'status')
        await setDoc(reviewRef, {
          action: 'pending',
          is_flagged: false,
          created_at: timestampISO,
          updated_at: timestampISO
        })

        // Create training subcollection with initial status
        const trainingRef = doc(db, 'audio', audioId, 'training', 'status')
        await setDoc(trainingRef, {
          trained_asr: false,
          tts_trained: false,
          training_sessions: [],
          created_at: timestampISO,
          updated_at: timestampISO
        })

        // Update user's contribution count (optional - don't fail if user doesn't exist)
        try {
          const userRef = doc(db, "users", userId)
          await updateDoc(userRef, {
            contribution_count: increment(1),
            updated_at: new Date()
          })
        } catch (userError) {
          console.warn(`Could not update user contribution count for ${userId}:`, userError)
          // Don't fail the upload if user update fails
        }

        results.push({
          filename: file.name,
          status: 'success',
          audioId,
          duration: fileMetadata.duration,
          structure: 'hierarchical'
        })
      } catch (error) {
        console.error(`Error processing ${file.name}:`, error)
        results.push({
          filename: file.name,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    const successCount = results.filter(r => r.status === 'success').length
    const errorCount = results.filter(r => r.status === 'error').length

    return NextResponse.json({
      success: successCount > 0,
      results,
      summary: {
        total: audioFiles.length,
        successful: successCount,
        failed: errorCount
      }
    })
  } catch (error) {
    console.error('Error in bulk upload:', error)
    return NextResponse.json(
      { error: 'Failed to process upload' },
      { status: 500 }
    )
  }
} 