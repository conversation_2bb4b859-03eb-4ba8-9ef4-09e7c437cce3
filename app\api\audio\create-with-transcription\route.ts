import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { auth, db } from '@/lib/firebase-admin'

export async function POST(request: NextRequest) {
  try {
    // Verify authentication using session cookie
    const cookieStore = await cookies()
    const sessionCookie = cookieStore.get('session')?.value

    if (!sessionCookie) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    // Verify the session cookie
    let decodedClaims
    try {
      decodedClaims = await auth.verifySessionCookie(sessionCookie, true)
    } catch (error) {
      console.error('Session verification failed:', error)
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 })
    }

    const currentUserId = decodedClaims.uid
    const audioData = await request.json()

    // Validate required fields
    const requiredFields = ['title', 'duration', 'format', 'user_id', 'source', 'audio_url', 'transcription', 'metadata']
    for (const field of requiredFields) {
      if (!audioData[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Verify user can only create audio for themselves (unless admin)
    const currentUserDoc = await db.collection('users').doc(currentUserId).get()
    const currentUserData = currentUserDoc.data()

    if (currentUserData?.role !== 'admin' && audioData.user_id !== currentUserId) {
      return NextResponse.json(
        { error: 'You can only create audio records for yourself' },
        { status: 403 }
      )
    }

    // Generate audio ID
    const audio_id = `audio_${Date.now()}`
    const timestamp = new Date().toISOString()

    // Create main audio document using Firebase Admin SDK
    await db.collection('audio').doc(audio_id).set({
      id: audio_id,
      title: audioData.title,
      audio_url: audioData.audio_url,
      duration: audioData.duration,
      format: audioData.format,
      created_at: timestamp,
      updated_at: timestamp,
      user_id: audioData.user_id,
      source: audioData.source
    })

    // Create transcription subcollection
    await db.collection('audio').doc(audio_id).collection('transcriptions').doc('primary').set({
      content: audioData.transcription.content,
      language: audioData.transcription.language || 'masalit',
      transcription_source: audioData.transcription.transcription_source || 'human/manual',
      type: audioData.transcription.type || 'txt',
      created_at: timestamp,
      updated_at: timestamp,
      speaker_count: 1
    })

    // Create metadata subcollection
    await db.collection('audio').doc(audio_id).collection('metadata').doc('details').set({
      gender: audioData.metadata.gender,
      language: audioData.metadata.language || 'masalit',
      recording_context: audioData.metadata.recording_context || 'direct_upload',
      created_at: timestamp,
      updated_at: timestamp
    })

    // Create review subcollection with initial status
    await db.collection('audio').doc(audio_id).collection('review').doc('status').set({
      action: 'pending',
      is_flagged: false,
      created_at: timestamp,
      updated_at: timestamp
    })

    // Create training subcollection with initial status
    await db.collection('audio').doc(audio_id).collection('training').doc('status').set({
      trained_asr: false,
      tts_trained: false,
      training_sessions: [],
      created_at: timestamp,
      updated_at: timestamp
    })

    return NextResponse.json({
      success: true,
      audio_id: audio_id,
      message: 'Audio record created successfully with hierarchical structure'
    })

  } catch (error) {
    console.error('Error creating audio with transcription:', error)
    return NextResponse.json(
      { error: 'Failed to create audio record' },
      { status: 500 }
    )
  }
}
